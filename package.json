{"name": "nha-muon-dan-edu", "version": "0.0.1", "private": true, "scripts": {"dev": "SELF_DESTROYING_SW='true' SUPPRESS_WARNING='true' vite dev --host", "build": "vite build", "preview": "npm run build && wrangler pages dev", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "generate-pwa-assets": "pwa-assets-generator", "sync-db-type": "rm -f ./src/lib/supabase/database.type.ts && supabase gen types --lang=typescript --project-id pifxstlnwfwizhiautwn > src/lib/supabase/database.types.ts"}, "devDependencies": {"@floating-ui/dom": "^1.6.11", "@iconify/svelte": "^4.0.2", "@skeletonlabs/skeleton": "^2.10.3", "@skeletonlabs/tw-plugin": "^0.4.0", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.46.0", "@sveltejs/adapter-vercel": "^5.4.6", "@sveltejs/kit": "^2.7.3", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/cookie": "^0.6.0", "@types/eslint": "^9.6.1", "@types/node": "^22.8.2", "@vite-pwa/assets-generator": "^0.2.6", "@vite-pwa/sveltekit": "^0.6.6", "autoprefixer": "^10.4.20", "clsx": "^2.1.1", "dayjs": "^1.11.13", "eslint": "^9.13.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.46.0", "formsnap": "^1.0.1", "hls.js": "^1.5.17", "jwt-decode": "^4.0.0", "prettier": "^3.3.3", "prettier-plugin-svelte": "^3.2.7", "prettier-plugin-tailwindcss": "^0.6.8", "svelte": "^5.1.4", "svelte-check": "^4.0.5", "sveltekit-superforms": "^2.20.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "typescript-eslint": "^8.12.2", "vite": "^5.4.10", "zod": "^3.23.8"}, "type": "module"}