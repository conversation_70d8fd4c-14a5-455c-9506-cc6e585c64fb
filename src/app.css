@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

* {
	-webkit-tap-highlight-color: transparent;
}

html,
body {
	@apply h-full overflow-hidden;
}

[data-fs-error] {
	@apply text-red-500;
}

@layer base {
	button {
		@apply transition-transform duration-75 active:scale-95;
	}

	vr {
		@apply divider-vertical;
	}

	input {
		@apply w-full rounded-full bg-transparent;
	}

	select {
		@apply w-full rounded-full bg-transparent;
	}
}

@layer components {
	.app-card {
		@apply flex w-full gap-2 items-center justify-between rounded-md border border-neutral-800 bg-teal-50 p-4 shadow-md dark:border-neutral-700 dark:bg-neutral-800;
		@apply disabled:pointer-events-none disabled:opacity-50;
	}
}
