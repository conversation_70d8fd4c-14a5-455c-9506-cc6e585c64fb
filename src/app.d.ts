import type { Database } from '$lib/supabase/database.types';
import type { Session, SupabaseClient, User } from '@supabase/supabase-js';
import 'vite-plugin-pwa/info';
import 'vite-plugin-pwa/pwa-assets';
import 'vite-plugin-pwa/svelte';

// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
	declare const __DATE__: string;
	declare const __RELOAD_SW__: boolean;

	interface Window {
		MSStream: unknown;
	}

	namespace App {
		interface Locals {
			buildDate: string;
			periodicUpdates: boolean;

			supabase: SupabaseClient<Database>;
			safeGetSession: () => Promise<{
				session: Session | null;
				user: User | null;
			}>;
			session: Session | null;
			user: User | null;
		}

		interface PageData {
			session: Session | null;
		}
	}
}

export {};
