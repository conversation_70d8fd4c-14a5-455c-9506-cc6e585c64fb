<script lang="ts">
	import { goto } from '$app/navigation';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import { Control, Field } from 'formsnap';
	import { superForm } from 'sveltekit-superforms';
	import type { PageData } from './$types';
	import { resetContestStorage } from '../contest/examLocalStorage';

	export let data: PageData;
	let { form } = data;
	$: ({ form } = data);

	let submitted = false;
	const toastStore = getToastStore();

	const startContestForm = superForm(form, {
		onResult({ result }) {
			if (result.type === 'success') {
				toastStore.trigger({
					background: 'variant-filled-success',
					message: result.data?.form.message,
					timeout: 1000,
					hideDismiss: true
				});
			} else if (result.type === 'failure') {
				toastStore.trigger({
					background: 'variant-filled-error',
					message: result.data?.form.message,
					timeout: 1000,
					hideDismiss: true
				});
			} else {
				toastStore.trigger({
					background: 'variant-filled-error',
					message: '<PERSON>ã có lỗi x<PERSON>y ra. Vui lòng thử lại sau.',
					timeout: 1000,
					hideDismiss: true
				});
			}
		},
		onUpdated({ form }) {
			if (form.valid) {
				submitted = true;
				const contestUrlParams = new URLSearchParams({
					contest_id: form.data.contestId,
					exam_id: form.data.examId,
					student_id: form.data.studentId
				});
				resetContestStorage();
				goto(`/contest?${contestUrlParams.toString()}`, { replaceState: true });
			}
		}
	});
	const { form: formData, enhance, submitting } = startContestForm;

	function handleBack() {
		window.history.back();
	}
</script>

<div class="flex size-full flex-col items-center gap-y-4 p-4">
	<h1 class="text-2xl font-bold">Các lưu ý khi làm bài thi</h1>

	<ul class="mb-4 list-disc space-y-3 pl-4">
		<li>Mỗi kỳ thi học viên chỉ được tham gia làm bài thi một lần duy nhất.</li>
		<li>Học viên phải hoàn thành tất cả các câu hỏi trong thời gian quy định.</li>
		<li>
			Học viên không được thoát khỏi trang làm bài thi, nếu thoát ra khi đang làm bài sẽ không được
			làm lại.
		</li>
		<li>
			Trong trường hợp bị mất kết nối mạng hoặc xảy ra sự cố khác, học viên cần liên hệ với quản trị
			viên để được hướng dẫn cách giải quyết. (Vui lòng chụp lại màn hình khi xảy ra sự cố).
		</li>
		<li>
			Học viên cần tự sắp xếp thời gian và địa điểm phù hợp để tránh bị gián đoạn khi làm bài thi.
		</li>
		<li>Những học viên không đạt trong kỳ thi sẽ chờ thông báo về lịch thi lại từ lớp học.</li>
	</ul>

	<form class="flex w-full gap-x-2" method="POST" use:enhance>
		<button
			type="button"
			class="variant-outline-warning btn flex-1 rounded-md"
			on:click={handleBack}
		>
			Quay lại
		</button>

		<button
			type="submit"
			class="variant-filled-success btn flex-1 rounded-md"
			disabled={$submitting || submitted}
		>
			Bắt đầu
		</button>

		<Field form={startContestForm} name="contestId">
			<Control let:attrs>
				<input {...attrs} bind:value={$formData.contestId} hidden />
			</Control>
		</Field>

		<Field form={startContestForm} name="examId">
			<Control let:attrs>
				<input {...attrs} bind:value={$formData.examId} hidden />
			</Control>
		</Field>

		<Field form={startContestForm} name="studentId">
			<Control let:attrs>
				<input {...attrs} bind:value={$formData.studentId} hidden />
			</Control>
		</Field>
	</form>
</div>
