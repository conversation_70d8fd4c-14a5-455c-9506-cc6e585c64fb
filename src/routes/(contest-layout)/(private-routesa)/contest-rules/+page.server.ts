import { startContestSchema, StudentExamStatus } from '$lib/schemas/contest.schema';
import { error } from '@sveltejs/kit';
import { fail, message, superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageServerLoad } from './$types';
import { chrono } from '$lib/utils/chrono';

export const load = (async ({ url }) => {
	const contestId = url.searchParams.get('contest_id');
	const studentId = url.searchParams.get('student_id');
	const examId = url.searchParams.get('exam_id');

	if (!contestId || !studentId || !examId) {
		error(400, 'Đã có lỗi xảy ra không thể bắt đầu bài thi');
	}

	const startContestData = {
		contestId: url.searchParams.get('contest_id') || '',
		examId: url.searchParams.get('exam_id') || '',
		studentId: url.searchParams.get('student_id') || ''
	};

	const form = await superValidate(startContestData, zod(startContestSchema), {
		errors: false
	});

	return { form };
}) satisfies PageServerLoad;

export const actions = {
	default: async ({ request, locals: { supabase } }) => {
		const form = await superValidate(request, zod(startContestSchema));

		if (!form.valid) return fail(400, { form });

		const { error } = await supabase.from('student_to_exams').insert({
			student_id: form.data.studentId,
			exam_id: form.data.examId,
			contest_id: form.data.contestId,
			status: StudentExamStatus.ENTRY,
			started_at: chrono().utc().toString(),
			ended_at: null,
			submission: []
		});

		if (error) {
			let errorMessage = 'Có lỗi xảy ra khi bắt đầu bài thi!';

			if (error.code === '23505') {
				await supabase
					.from('student_to_exams')
					.update({
						status: StudentExamStatus.INTERUPPTED
					})
					.eq('student_id', form.data.studentId)
					.eq('exam_id', form.data.examId)
					.eq('contest_id', form.data.contestId);

				errorMessage =
					'Bài thi đã bị gián đoạn trong quá trình làm bài. Vui lòng liên hệ quản trị viên để được hỗ trợ.';
			}

			return message(form, errorMessage, {
				status: 400
			});
		}

		return message(form, 'Bắt đầu bài thi thành công!');
	}
};
