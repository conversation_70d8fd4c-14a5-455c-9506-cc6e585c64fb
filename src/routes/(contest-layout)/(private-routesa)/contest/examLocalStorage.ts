import type { ExamSubmissionSchema, Question } from '$lib/schemas/contest.schema';
import { persisted } from '$lib/utils/persistedStore';

type ContestStorage = {
    currentQuestionIndex: number;
	startTime: number;
	timeLimit: number;
	questions: Question[];
	contestId: string;
	studentId: string;
	examId: string;
	submissions: ExamSubmissionSchema['submissions'];
};

export const contestStorage = persisted<ContestStorage>('contest', {
    currentQuestionIndex: 0,
	startTime: 0,
	timeLimit: 0,
	questions: [],
	contestId: '',
	studentId: '',
	examId: '',
	submissions: []
});


export const resetContestStorage = () => {
	contestStorage.set({
		currentQuestionIndex: 0,
		startTime: 0,
		timeLimit: 0,
		questions: [],
		contestId: '',
		studentId: '',
		examId: '',
		submissions: []
	});
}