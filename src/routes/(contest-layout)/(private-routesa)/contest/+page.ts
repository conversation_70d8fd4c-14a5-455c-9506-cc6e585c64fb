import { suffleArray } from '$lib';
import {
	examSubmissionSchema,
	StudentExamStatus,
	type Question
} from '$lib/schemas/contest.schema';
import { error } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { contestStorage } from './examLocalStorage';
import { get } from 'svelte/store';

export const load = async ({ url, parent }) => {
	const contestId = url.searchParams.get('contest_id');
	const studentId = url.searchParams.get('student_id');
	const examId = url.searchParams.get('exam_id');

	if (!contestId || !studentId || !examId) {
		error(400, 'Đã có lỗi xảy ra không thể bắt đầu bài thi');
	}

	const localStorageContest = get(contestStorage);

	if (
		localStorageContest.contestId === contestId &&
		localStorageContest.studentId === studentId &&
		localStorageContest.examId === examId
	) {
		const form = await superValidate(
			{
				contestId: localStorageContest.contestId,
				studentId: localStorageContest.studentId,
				examId: localStorageContest.examId,
				submissions: localStorageContest.submissions
			},
			zod(examSubmissionSchema)
		);

		return {
			form,
			startTime: localStorageContest.startTime,
			timeLimit: localStorageContest.timeLimit,
			questions: localStorageContest.questions
		};
	}

	const { supabase } = await parent();

	const { data: studentExam } = await supabase
		.from('student_to_exams')
		.select('status')
		.eq('contest_id', contestId)
		.eq('exam_id', examId)
		.eq('student_id', studentId)
		.maybeSingle();

	if (studentExam?.status === StudentExamStatus.ENTRY) {
		const { error: updatedStudentExamError } = await supabase
			.from('student_to_exams')
			.update({ status: StudentExamStatus.IN_PROGRESS })
			.eq('contest_id', contestId)
			.eq('exam_id', examId)
			.eq('student_id', studentId);

		if (updatedStudentExamError) error(400, 'Đã có lỗi xảy ra không thể bắt đầu bài thi');
	}

	const { data: exam } = await supabase
		.from('exams')
		.select('time_limit, questions')
		.eq('id', examId)
		.single<{ time_limit: number; questions: Question[] }>();

	if (!exam) error(404, 'Không tìm thấy bài thi');

	exam.questions = exam.questions.map((question) => {
		question.answers = question.answers.map(({ id, answer }) => ({ id, answer, selected: false }));
		return question;
	});

	suffleArray(exam.questions);

	const form = await superValidate(
		{
			contestId,
			studentId,
			examId,
			submissions: []
		},
		zod(examSubmissionSchema)
	);

	contestStorage.set({
		currentQuestionIndex: 0,
		startTime: Date.now(),
		timeLimit: exam.time_limit * 60,
		questions: exam.questions,
		contestId,
		studentId,
		examId,
		submissions: []
	});

	return { form, timeLimit: exam.time_limit * 60, questions: exam.questions };
};
