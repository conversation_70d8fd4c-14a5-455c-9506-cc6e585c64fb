<script lang="ts">
	import { goto } from '$app/navigation';
	import { cn } from '$lib';
	import {
		examSubmissionSchema,
		StudentExamStatus,
		type Question
	} from '$lib/schemas/contest.schema';
	import type { DatabaseRow } from '$lib/supabase/types-utils';
	import { chrono } from '$lib/utils/chrono';
	import { getModalStore, getToastStore, type ModalSettings } from '@skeletonlabs/skeleton';
	import { onMount } from 'svelte';
	import { setMessage, superForm } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import type { PageData } from './$types';
	import { contestStorage, resetContestStorage } from './examLocalStorage';

	export let data: PageData;
	let { form, timeLimit, questions, supabase } = data;
	$: ({ form, timeLimit, questions, supabase } = data);

	const toastStore = getToastStore();
	const modalStore = getModalStore();

	let submitted = false;
	let interval: NodeJS.Timeout;
	let _timeRemaining =
		timeLimit - parseInt(((Date.now() - $contestStorage.startTime) / 1000).toString());
	let currentQuestionIndex = $contestStorage.currentQuestionIndex ?? 0;

	const submitModalConfirmationSetting: ModalSettings = {
		type: 'confirm',
		// Data
		title: 'Xác nhận nộp bài thi',
		body: 'Bạn có chắc chắn muốn nộp bài thi không? Bạn sẽ không thể quay lại sau khi nộp bài.',
		buttonTextConfirm: 'Nộp bài',
		buttonTextCancel: 'Hủy',
		// TRUE if confirm pressed, FALSE if cancel pressed
		response: (r: boolean) => {
			if (r && !$submitting) {
				examSubmissionForm.submit();
			} else {
				submitted = false;
			}
		}
	};

	const examSubmissionForm = superForm(form, {
		id: 'examSubmissionForm',
		SPA: true,
		validators: zod(examSubmissionSchema),
		invalidateAll: false,
		resetForm: false,
		dataType: 'json',
		onResult({ result }) {
			if (result.type === 'success') {
				toastStore.trigger({
					background: 'variant-filled-success',
					message: 'Nộp bài thành công',
					timeout: 1000,
					hideDismiss: true
				});
			} else if (result.type === 'failure') {
				toastStore.trigger({
					background: 'variant-filled-error',
					message: result.data?.form.message,
					timeout: 1000,
					hideDismiss: true
				});
			} else {
				toastStore.trigger({
					background: 'variant-filled-error',
					message: 'Đã có lỗi xảy ra. Vui lòng thử lại sau.',
					timeout: 1000,
					hideDismiss: true
				});
			}
		},
		async onUpdated({ form }) {
			if (form.valid) {
				submitted = true;

				const { data: exam } = await supabase
					.from('exams')
					.select('*')
					.eq('id', form.data.examId)
					.single<Omit<DatabaseRow<'exams'>, 'questions'> & { questions: Question[] }>();

				if (!exam) {
					return setMessage(form, 'Không tìm thấy bài thi', { status: 404 });
				}

				const { data: contestResults } = await supabase
					.from('student_to_exams')
					.select('contest_id, status')
					.eq('student_id', form.data.studentId)
					.eq('contest_id', form.data.contestId)
					.eq('exam_id', form.data.examId);

				if (contestResults?.some((result) => result.status === StudentExamStatus.PASSED)) {
					return setMessage(
						form,
						'Đã có bài thi được nộp trước đó. Bạn không thể nộp bài thi này.',
						{ status: 400 }
					);
				}

				const pointForEachQuestion = exam.max_score / exam.questions.length;
				const finalScore = form.data.submissions.reduce((acc, { questionId, answerId }) => {
					const question = exam.questions.find((q) => q.id === questionId);
					if (!question) return acc;

					const answer = question.answers.find((a) => a.id === answerId);
					if (!answer) return acc;

					return answer.is_correct ? acc + pointForEachQuestion : acc;
				}, 0);

				const { error: submissionError } = await supabase
					.from('student_to_exams')
					.update({
						status:
							finalScore >= exam.min_score_to_pass
								? StudentExamStatus.PASSED
								: StudentExamStatus.FAILED,
						ended_at: chrono().utc().toString(),
						submission: form.data.submissions
					})
					.eq('student_id', form.data.studentId)
					.eq('contest_id', form.data.contestId)
					.eq('exam_id', form.data.examId);

				if (submissionError) {
					return setMessage(
						form,
						'Có lỗi xảy ra khi nộp bài thi! Vui lòng liên hệ quản trị viên để được hỗ trợ.',
						{ status: 400 }
					);
				} else {
					resetContestStorage();
					const searchParams = new URLSearchParams(location.search);
					goto(`/contest-result?${searchParams.toString()}`);
				}
			}
		}
	});
	const { form: formData, submitting, enhance } = examSubmissionForm;

	function startCountdown() {
		interval = setInterval(() => {
			_timeRemaining--;
			if (_timeRemaining <= 0) {
				examSubmissionForm.submit();
				clearInterval(interval);
			}
		}, 1000);
	}

	function onAnswerChange(event: Event & { currentTarget: EventTarget & HTMLInputElement }) {
		const questionId = questions[currentQuestionIndex].id;
		const target = event.target as HTMLInputElement;
		formData.update((data) => {
			!data.submissions.find((submission) => submission.questionId === questionId)
				? data.submissions.push({ questionId, answerId: target.value })
				: (data.submissions = data.submissions.map((submission) =>
						submission.questionId === questionId
							? { ...submission, answerId: target.value }
							: submission
					));

			return data;
		});

		$contestStorage.currentQuestionIndex = currentQuestionIndex;
		$contestStorage.submissions = $formData.submissions;
	}

	// async function handleVisibilityChange() {
	// 	if (document.visibilityState === 'hidden') {
	// 		await supabase
	// 			.from('student_to_exams')
	// 			.update({
	// 				status: StudentExamStatus.INTERUPPTED,
	// 				ended_at: chrono().utc().toString(),
	// 				submission: $formData.submissions
	// 			})
	// 			.eq('student_id', form.data.studentId)
	// 			.eq('exam_id', form.data.examId)
	// 			.eq('contest_id', form.data.contestId);

	// 		alert(
	// 			'Bài thi đã bị gián đoạn trong quá trình làm bài. Vui lòng liên hệ quản trị viên để được hỗ trợ.'
	// 		);

	// 		goto('/educations');

	// 		clearInterval(interval);
	// 	}
	// }

	onMount(() => {
		startCountdown();

		// auto select first answer for all questions to debug

		// $formData.submissions = exam.questions.map((question) => ({
		// 	questionId: question.id,
		// 	answerId: question.answers[0].id
		// }));

		// currentQuestionIndex = exam.questions.length - 1;
		return () => clearInterval(interval);
	});
</script>

<form class="z-50 flex size-full touch-manipulation flex-col" use:enhance>
	<input type="hidden" name="examId" bind:value={$formData.examId} />
	<input type="hidden" name="studentId" bind:value={$formData.studentId} />
	<input type="hidden" name="contestId" bind:value={$formData.contestId} />

	{#if _timeRemaining <= 0}
		<div class="flex h-full w-full flex-col items-center justify-center gap-y-4 border-b p-4">
			<p class="text-4xl font-semibold text-red-400">00:00</p>
			<p class="text-center">
				Thời gian làm bài đã hết. Bạn sẽ không thể tiếp tục làm bài. Vui lòng nộp bài.
			</p>

			<button type="submit" class="variant-filled-success btn" disabled={$submitting}>
				Nộp bài
			</button>
		</div>
	{:else}
		<div class="w-full border-b border-neutral-900 p-4 dark:border-teal-500">
			<p>Thời gian làm bài</p>
			<p
				class={cn('font-semibold', {
					'text-yellow-600 dark:text-yellow-400': _timeRemaining < 600,
					'text-red-500 dark:text-red-600': _timeRemaining < 300
				})}
			>
				{Math.floor(_timeRemaining / 60)} phút {_timeRemaining % 60} giây
			</p>
		</div>

		<div class="flex w-full">
			{#if currentQuestionIndex > 0}
				<button
					type="button"
					class="variant-filled-warning btn flex-1"
					on:click={() => currentQuestionIndex--}
				>
					Câu trước
				</button>
			{/if}

			{#if currentQuestionIndex < questions.length - 1}
				<button
					type="button"
					class="variant-filled-success btn flex-1"
					disabled={!$formData.submissions.find(
						(submission) => submission.questionId === questions[currentQuestionIndex].id
					)?.answerId ||
						_timeRemaining <= 0 ||
						$submitting ||
						submitted}
					on:click={() => currentQuestionIndex++}
				>
					Câu tiếp
				</button>
			{/if}

			{#if currentQuestionIndex === questions.length - 1}
				<button
					type="button"
					class="variant-filled-success btn flex-1"
					disabled={!$formData.submissions.find(
						(submission) => submission.questionId === questions[currentQuestionIndex].id
					)?.answerId ||
						_timeRemaining <= 0 ||
						$submitting ||
						submitted}
					on:click={() => modalStore.trigger(submitModalConfirmationSetting)}
				>
					Nộp bài
				</button>
			{/if}
		</div>

		{#key currentQuestionIndex}
			<div class="w-full border-b border-neutral-900 p-4 dark:border-teal-500">
				<p>({currentQuestionIndex + 1}/{questions.length})</p>
				<p>Câu hỏi:</p>
				<p class="font-semibold">
					{questions[currentQuestionIndex].question}
				</p>
			</div>

			<div class="mb-4 w-full space-y-2 p-4">
				<p>Vui lòng chọn 1 trong {questions[currentQuestionIndex].answers.length} đáp án:</p>

				<div class="space-y-4 pl-4">
					{#each questions[currentQuestionIndex].answers as { id, answer }, index (id)}
						<label class="flex items-center space-x-2">
							<input
								class="radio"
								type="radio"
								name="answer"
								value={id}
								checked={$formData.submissions.find(
									(submission) => submission.questionId === questions[currentQuestionIndex].id
								)?.answerId === id}
								disabled={_timeRemaining <= 0}
								on:change={onAnswerChange}
							/>
							<p>{answer}</p>
						</label>
					{/each}
				</div>
			</div>
		{/key}
	{/if}
</form>
