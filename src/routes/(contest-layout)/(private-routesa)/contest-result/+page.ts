import { error } from '@sveltejs/kit';

export const load = async ({ url, parent }) => {
	const contestId = url.searchParams.get('contest_id');
	const studentId = url.searchParams.get('student_id');
	const examId = url.searchParams.get('exam_id');

	if (!contestId || !studentId || !examId) {
		error(400, 'Đã có lỗi xảy ra không thể lấy kết quả bài thi');
	}

	const { supabase } = await parent();

	const { data, error: resultError } = await supabase
		.from('student_to_exams')
		.select('status')
		.eq('contest_id', contestId)
		.eq('exam_id', examId)
		.eq('student_id', studentId)
		.single();

	if (resultError) {
		error(400, 'Đã có lỗi xảy ra không thể lấy kết quả bài thi');
	}

	return { status: data?.status };
};
