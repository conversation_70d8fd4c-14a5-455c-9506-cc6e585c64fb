<script lang="ts">
	import { goto } from '$app/navigation';
	import { cn } from '$lib';
	import { StudentExamStatus } from '$lib/schemas/contest.schema';

	let { data } = $props();
</script>

<div class="z-50 flex size-full flex-col">
	<div class="flex h-full w-full flex-col items-center justify-center gap-y-4 border-b p-4">
		{#if data.status}
			{@const isPasses = data.status === StudentExamStatus.PASSED}
			<p class={cn('text-center text-2xl text-red-400', { 'text-green-400': isPasses })}>Kết quả</p>

			<p class={cn('text-red-400', { 'text-green-400': isPasses })}>
				{isPasses ? 'Chúc mừng bạn đã vượt qua kỳ thi' : 'Rất tiếc, bạn đã không vượt qua kỳ thi'}
			</p>

			<button type="button" class="variant-filled-success btn" onclick={() => goto('/educations')}>
				Quay lại
			</button>
		{/if}
	</div>
</div>
