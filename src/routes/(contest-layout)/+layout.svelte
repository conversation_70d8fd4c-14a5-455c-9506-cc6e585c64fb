<script lang="ts">
	function preventSwipeBack(e: TouchEvent & { currentTarget: EventTarget & HTMLElement }) {
		// is not near edge of view, exit
		if (e.changedTouches[0].pageX > 32 && e.changedTouches[0].pageX < window.innerWidth - 32)
			return;

		// prevent swipe to navigate gesture
		e.preventDefault();
	}
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
<section
	class="size-full overflow-auto"
	on:touchstart={preventSwipeBack}
	on:contextmenu|preventDefault
>
	<slot />
</section>
