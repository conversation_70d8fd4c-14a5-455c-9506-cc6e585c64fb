<script context="module">
	import '../app.css';
</script>

<script lang="ts">
	import { invalidate } from '$app/navigation';
	import EditProfile from '$lib/components/drawers/EditProfile.svelte';
	import { arrow, autoUpdate, computePosition, flip, offset, shift } from '@floating-ui/dom';
	import {
		Drawer,
		getDrawerStore,
		initializeStores,
		Modal,
		storePopup,
		Toast,
		type ModalComponent
	} from '@skeletonlabs/skeleton';
	import { onMount } from 'svelte';
	import { pwaInfo } from 'virtual:pwa-info';
	import { pwaAssetsHead } from 'virtual:pwa-assets/head';

	$: webManifest = pwaInfo ? pwaInfo.webManifest.linkTag : '';

	storePopup.set({ computePosition, autoUpdate, flip, shift, offset, arrow });
	initializeStores();

	const drawerStore = getDrawerStore();

	export let data;
	$: ({ session, supabase } = data);

	const modalRegistry: Record<string, ModalComponent> = {};

	onMount(() => {
		const { data } = supabase.auth.onAuthStateChange((_, newSession) => {
			if (newSession?.expires_at !== session?.expires_at) {
				invalidate('supabase:auth');
			}
		});

		return () => data.subscription.unsubscribe();
	});
</script>

<svelte:head>
	{#if pwaAssetsHead.themeColor}
		<meta name="theme-color" content={pwaAssetsHead.themeColor.content} />
	{/if}
	{#each pwaAssetsHead.links as link}
		<link {...link} />
	{/each}
	<!-- eslint-disable-next-line svelte/no-at-html-tags -->
	{@html webManifest}
</svelte:head>

<Toast rounded="rounded-full" position="tr" />
<Modal components={modalRegistry} />
<Drawer
	position="right"
	bgDrawer="bg-primary-900 text-white"
	bgBackdrop="bg-black/70"
	width="w-[75%]"
	padding="p-4"
	rounded="rounded-xl"
>
	{#if $drawerStore.id === 'student-details'}
		<!-- <StudentDetails /> -->
	{:else if $drawerStore.id === 'edit-profile'}
		<EditProfile />
	{:else}
		<!-- (fallback contents) -->
	{/if}
</Drawer>

<slot />

{#await import('$lib/components/ReloadPrompt.svelte') then { default: ReloadPrompt }}
	<ReloadPrompt />
{/await}
