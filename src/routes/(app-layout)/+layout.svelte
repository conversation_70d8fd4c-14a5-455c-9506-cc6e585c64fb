<script lang="ts">
	import { afterNavigate, beforeNavigate } from '$app/navigation';
	import Navigation from '$lib/components/Navigation.svelte';
	import { chrono } from '$lib/utils/chrono';
	import { persisted } from '$lib/utils/persistedStore.js';
	import Icon from '@iconify/svelte';
	import { AppBar, AppShell } from '@skeletonlabs/skeleton';
	import { onMount } from 'svelte';

	export let data;
	$: ({ session } = data);

	let isNavigating = false;
	let isDarkMode = persisted('darkMode', false);
	let utcTime = chrono();
	let localTime = chrono().utc(true);


	beforeNavigate(() => {
		isNavigating = true;
	});

	afterNavigate(() => {
		isNavigating = false;
	});

	onMount(() => {
		const interval = setInterval(() => {
			utcTime = chrono();
			localTime = chrono().utc(true);
		}, 1000);

		return () => clearInterval(interval);
	});
</script>

<AppShell
	class="max-h-dvh touch-manipulation bg-teal-800 text-gray-900 dark:text-white"
	slotPageContent="flex-1 h-1"
	slotHeader="select-none"
>
	<svelte:fragment slot="header">
		<AppBar class="!bg-transparent bg-center bg-no-repeat" slotLead="flex gap-x-3 ">
			<svelte:fragment slot="lead">
				<a href="/audio-books">
					<figure class="rounded-full bg-white">
						<img src="/images/logo.png" alt="logo" class="size-10" />
					</figure>
				</a>

				<span class="text-xl font-semibold text-white"> Nhà Muôn Dân </span>
			</svelte:fragment>

			<svelte:fragment slot="trail">
				<button
					on:click={() => {
						document.documentElement.classList.toggle('dark');
						$isDarkMode = document.documentElement.classList.contains('dark');
					}}
				>
					<Icon
						icon={$isDarkMode
							? 'material-symbols:light-mode-outline-rounded'
							: 'material-symbols:dark-mode-outline-rounded'}
						class="text-white"
						width={32}
					/>
				</button>

				<a class="overflow-hidden rounded-full" href="/settings">
					{#if session}
						<figure class="bg-white">
							<img
								src={session?.user.identities?.[0].identity_data?.avatar_url}
								alt="avatar"
								class="size-10"
							/>
						</figure>
					{:else}
						<Icon icon="mdi:account-circle" class="text-white" width={40} />
					{/if}
				</a>
			</svelte:fragment>
		</AppBar>
	</svelte:fragment>

	<section
		class="flex h-full flex-col overflow-hidden rounded-t-2xl border-b border-neutral-900 bg-neutral-100 p-4 dark:border-slate-300 dark:bg-neutral-900"
	>
		{#if isNavigating}
			<div
				class="absolute left-0 top-0 z-50 flex h-dvh w-screen select-none items-center justify-center"
			>
				<div class="flex items-center justify-center rounded-lg bg-white p-4">
					<Icon icon="mdi:loading" class="animate-spin text-black" width={48} />
				</div>
			</div>
		{/if}
		<div class="relative h-1 flex-1 overflow-auto pb-2">
			<slot />
		</div>

		<p class="text-xs"><b>UTC: </b> {utcTime}</p>
		<p class="text-xs"><b>Local: </b> {localTime}</p>
	</section>

	<svelte:fragment slot="footer">
		<Navigation />
	</svelte:fragment>
</AppShell>
