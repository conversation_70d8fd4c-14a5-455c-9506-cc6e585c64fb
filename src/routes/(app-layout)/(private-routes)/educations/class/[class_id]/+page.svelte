<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/stores';
	import { cn } from '$lib';
	import Chip from '$lib/components/Chip.svelte';
	import Icon from '@iconify/svelte';

	let { data } = $props();
	let { classDetail, studentTrainingProgram } = $derived(data);
	let skippedStudentTrainingProgram = $derived(
		studentTrainingProgram.filter((data) => data?.skipped)
	);
	let nonSkippedStudentTrainingProgram = $derived(
		studentTrainingProgram.filter((data) => !data?.skipped)
	);
</script>

<section class="flex flex-col gap-y-4">
	<div class="flex items-center gap-x-2">
		<button onclick={() => goto(`/educations`)}>
			<Icon icon="mdi:chevron-left" width={32} />
		</button>

		<h1 class="text-xl font-bold">
			{classDetail.name}
		</h1>
	</div>

	<section class="flex flex-col gap-y-4">
		{#each skippedStudentTrainingProgram as data (data?.subject_id)}
			{#if data}
				<div class="flex flex-col">
					<div class="app-card rounded-b-none">
						<div class="flex flex-col items-start gap-y-3 overflow-hidden font-semibold">
							<h2 class="w-full truncate text-left text-xl">
								{data.subjectName}
							</h2>

							<div class="flex flex-wrap gap-2 text-white">
								<Chip class="rounded-full bg-amber-600 px-3 py-0.5 text-sm">Bảo lưu</Chip>
							</div>
						</div>
					</div>

					<div class="flex w-full">
						<button
							class="flex-1 rounded-b-md border border-t-0 border-neutral-800 bg-sky-400 p-3 text-center font-semibold text-white disabled:scale-100 disabled:bg-gray-400 disabled:opacity-50 dark:border-sky-500 dark:bg-sky-700"
							onclick={() => goto(`${$page.url.pathname}/subject/${data.subject_id}`)}
							disabled={data.hasTodayContest}
						>
							{#if data.hasTodayContest}
								Đang thi
							{:else}
								Học tập
							{/if}
						</button>
					</div>
				</div>
			{/if}
		{/each}
		{#each nonSkippedStudentTrainingProgram as data, index (data?.subject_id)}
			{#if data}
				{@const completedAmount = data.lessons.filter((lesson) => lesson.completed).length}
				{@const totalAmount = data.lessons.length}
				{@const previousData = index > 0 ? nonSkippedStudentTrainingProgram[index - 1] : null}
				{@const hasCompleteCurrent = data.lessons.every((lesson) => lesson.completed)}
				{@const hasCompletedPrevious = previousData?.lessons.every((lesson) => lesson.completed)}
				{@const hasFailedPrevious = previousData?.failed}
				{@const hasPassedPrevious = previousData?.passed}
				{@const hasDidExamPrevious = previousData?.didExam}
				{@const isOfflineClass = classDetail.tags?.split(',').includes('offline')}
				{@const isLearnOfflineClass = classDetail.tags?.split(',').includes('learn-offline')}
				{@const shouldDisable = isLearnOfflineClass
					? !data.hasTodayContest
					: isOfflineClass
						? !data.paid || (index > 0 && !hasCompletedPrevious)
						: !data.paid ||
							(index > 0 &&
								(!hasCompletedPrevious ||
									hasFailedPrevious ||
									!hasPassedPrevious ||
									!hasDidExamPrevious))}

				<div class="flex flex-col">
					<div
						class={cn('app-card rounded-b-none', {
							'opacity-50': shouldDisable
						})}
					>
						<div class="flex flex-col items-start gap-y-3 overflow-hidden font-semibold">
							<h2 class="w-full truncate text-left text-xl">
								{data.subjectName}
							</h2>

							<Chip
								class={cn('bg-emerald-600', {
									'bg-sky-600': completedAmount < totalAmount,
									'bg-rose-600': !completedAmount
								})}
							>
								Hoàn thành {completedAmount}/{totalAmount} bài học
							</Chip>

							<div class="flex flex-wrap gap-2 text-white">
								<Chip
									class={cn('rounded-full bg-emerald-600 px-3 py-0.5 text-sm', {
										'bg-rose-600': !data.paid
									})}
								>
									{data.paid ? 'Đã thanh toán' : 'Chưa thanh toán'}
								</Chip>

								{#if !isOfflineClass}
									<Chip
										class={cn('bg-emerald-600', {
											'bg-rose-600': !data.didExam || data.failed,
											'bg-slate-600': !data.examId
										})}
									>
										{#if data.examId}
											{data.passed ? 'Đã đạt' : data.failed ? 'Chưa đạt' : 'Chưa thi'}
										{:else}
											Chưa có bài kiểm tra
										{/if}
									</Chip>
								{/if}
							</div>
						</div>
					</div>

					{#if !shouldDisable}
						{#if !data.paid}
							<button
								class="flex-1 rounded-b-md border border-t-0 border-neutral-800 bg-emerald-500 p-3 text-center font-semibold text-white dark:border-amber-500 dark:bg-amber-700"
							>
								Đóng học phí
							</button>
						{:else}
							<div class="flex w-full">
								{#if !isLearnOfflineClass}
									<button
										class="flex-1 rounded-b-md border border-t-0 border-neutral-800 bg-sky-400 p-3 text-center font-semibold text-white disabled:scale-100 disabled:bg-gray-400 disabled:opacity-50 dark:border-sky-500 dark:bg-sky-700"
										onclick={() => goto(`${$page.url.pathname}/subject/${data.subject_id}`)}
										disabled={data.hasTodayContest}
									>
										{#if data.hasTodayContest}
											Đang thi
										{:else}
											Học tập
										{/if}
									</button>
								{/if}

								{#if !isOfflineClass}
									<button
										class="flex-1 rounded-b-md border border-t-0 border-neutral-800 bg-orange-400 p-3 text-center font-semibold text-white disabled:scale-100 disabled:bg-gray-400 disabled:opacity-50 dark:border-amber-500 dark:bg-amber-700"
										disabled={!hasCompleteCurrent && !isLearnOfflineClass}
										onclick={async () => {
											await invalidate('educations:layout:class_id');
											await goto(`${$page.url.pathname}/exams/${data.subject_id}`);
										}}
									>
										Lịch thi
									</button>
								{/if}
							</div>
						{/if}
					{/if}
				</div>
			{/if}
		{/each}
	</section>
</section>
