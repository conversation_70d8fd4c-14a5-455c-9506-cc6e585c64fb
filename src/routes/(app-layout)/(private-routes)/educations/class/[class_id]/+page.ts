import { StudentExamStatus } from '$lib/schemas/contest.schema';
import { chrono } from '$lib/utils/chrono';
import { error } from '@sveltejs/kit';

type StudentDetails = {
	id: string;
	paid_tuition_subjects: (string | undefined)[];
	skipped_subjects: (string | undefined)[];
};

export const load = async ({ parent, depends, params }) => {
	depends('educations:layout:class_id');

	const { supabase, session } = await parent();

	if (!session) error(401, 'Unauthorized');

	const classId = params.class_id;
	const failResponse = { classDetail: { name: 'N/A', tags: '' }, studentTrainingProgram: [] };

	const [studentResponse, classesResponse, classTrainingProgramResponse, contestsResponse] =
		await Promise.all([
			supabase
				.from('students')
				.select('id, paid_tuition_subjects, skipped_subjects')
				.eq('profile_id', session.user.id)
				.eq('class_id', classId)
				.single<StudentDetails>(),

			supabase.from('classes').select('name, tags').eq('id', classId).single(),

			supabase
				.from('traning_programs_to_classes')
				.select('training_programs_id')
				.eq('class_id', classId)
				.single(),

			supabase
				.from('contest')
				.select('id, start_date, end_date')
				.eq('class_id', classId)
				.order('start_date', { ascending: true })
		]);

	const student = studentResponse.data;
	const classDetail = classesResponse.data;
	const trainingProgramByClassId = classTrainingProgramResponse.data;
	const classContests = (contestsResponse.data || []).sort(
		(a, b) => new Date(a!.start_date).getTime() - new Date(b!.start_date).getTime()
	);

	if (!student || !classDetail || !trainingProgramByClassId) return failResponse;

	const { data: trainingProgramsSubjects } = await supabase
		.from('training_program_to_subjects')
		.select('order, subject_id')
		.eq('training_programs_id', trainingProgramByClassId.training_programs_id)
		.order('order', { ascending: true });

	if (!trainingProgramsSubjects) return failResponse;

	const { data: subjects } = await supabase
		.from('subjects')
		.select('id, name, exam_id')
		.in(
			'id',
			trainingProgramsSubjects.map((item) => item.subject_id)
		);

	if (!subjects) return failResponse;

	const { data: contestsOnExam } = await supabase
		.from('contest_on_exam')
		.select('*')
		.in('exam_id', subjects.map((item) => item.exam_id).filter(Boolean))
		.in(
			'contest_id',
			classContests.map((item) => item.id)
		);

	if (!contestsOnExam) return failResponse;

	const { data: subjectLessons } = await supabase
		.from('subject_to_lessons')
		.select('order, subject_id, lesson_id')
		.in(
			'subject_id',
			trainingProgramsSubjects.map((item) => item.subject_id)
		)
		.order('order', { ascending: true });

	if (!subjectLessons) return failResponse;

	const { data: studentLessons } = await supabase
		.from('student_to_lessons')
		.select('completed_at, lesson_id')
		.eq('student_id', student.id);

	if (!studentLessons) return failResponse;

	const studentTrainingProgram = await Promise.all(
		trainingProgramsSubjects.map(async (subject) => {
			const lessons = subjectLessons.filter((lesson) => lesson.subject_id === subject.subject_id);

			const completedLessons = studentLessons.filter((studentLesson) =>
				lessons.find((lesson) => lesson.lesson_id === studentLesson.lesson_id)
			);

			const subjectDetails = subjects.find((item) => item.id === subject.subject_id);

			const examId = subjectDetails?.exam_id;

			if (!subjectDetails) return null;

			if (examId) {
				const contestIdsOfExam = contestsOnExam
					.filter((item) => item.exam_id === examId)
					.map((item) => item.contest_id);

				const { data: contestResults } = await supabase
					.from('student_to_exams')
					.select('contest_id, status')
					.eq('student_id', student.id)
					.eq('exam_id', examId)
					.in('contest_id', contestIdsOfExam);

				if (!contestResults) return null;

				for await (const result of contestResults) {
					if (result?.status === StudentExamStatus.IN_PROGRESS) {
						await supabase
							.from('student_to_exams')
							.update({ status: StudentExamStatus.INTERUPPTED })
							.eq('student_id', student.id)
							.eq('exam_id', examId)
							.eq('contest_id', result.contest_id);
					}
				}

				const { data: incomingContestsOfExam } = await supabase
					.from('contest_on_exam')
					.select('contest_id')
					.eq('exam_id', examId);

				const hasTodayContest = classContests.some((contest) => {
					const offsetStartDate = chrono(contest.start_date);
					const offsetEndDate = chrono(contest.end_date);

					return (
						offsetStartDate.isSameOrBefore(
							chrono().utc(false).subtract(chrono().utcOffset(), 'minutes'),
							'date'
						) &&
						offsetEndDate.isSameOrAfter(
							chrono().utc(false).subtract(chrono().utcOffset(), 'minutes'),
							'date'
						) &&
						incomingContestsOfExam?.some((item) => item.contest_id === contest.id)
					);
				});

				return {
					...subject,
					subjectName: subjectDetails?.name || 'N/A',
					examId,
					hasTodayContest,
					didExam: contestResults.some((result) => result !== null),
					passed: contestResults.some((result) => result?.status === StudentExamStatus.PASSED),
					failed: contestResults.every((result) => result?.status !== StudentExamStatus.PASSED),
					paid: student.paid_tuition_subjects.includes(subject.subject_id),
					skipped: student.skipped_subjects.includes(subject.subject_id),
					lessons: lessons.map((lesson) => ({
						...lesson,
						completed: !!completedLessons.find(
							(studentLesson) => studentLesson.lesson_id === lesson.lesson_id
						)?.completed_at
					}))
				};
			} else {
				return {
					...subject,
					subjectName: subjectDetails?.name || 'N/A',
					examId,
					hasTodayContest: false,
					didExam: false,
					passed: false,
					failed: false,
					paid: student.paid_tuition_subjects.includes(subject.subject_id),
					skipped: student.skipped_subjects.includes(subject.subject_id),
					lessons: lessons.map((lesson) => ({
						...lesson,
						completed: !!completedLessons.find(
							(studentLesson) => studentLesson.lesson_id === lesson.lesson_id
						)?.completed_at
					}))
				};
			}
		})
	);

	return { student, classDetail, classContests, studentTrainingProgram };
};
