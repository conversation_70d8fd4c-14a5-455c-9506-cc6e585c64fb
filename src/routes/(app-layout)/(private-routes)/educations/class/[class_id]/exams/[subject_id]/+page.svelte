<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/stores';
	import { cn } from '$lib';
	import Chip from '$lib/components/Chip.svelte';
	import { StudentExamStatus } from '$lib/schemas/contest.schema';
	import { contestStatusToVietnamese } from '$lib/translate';
	import Icon from '@iconify/svelte';
	import type { PageData } from './$types';
	import { chrono } from '$lib/utils/chrono';

	export let data: PageData;
	let { student, examId, subjectName, contests, todayContestId } = data;
	$: ({ student, examId, subjectName, contests, todayContestId } = data);

	console.log('todayContestId', todayContestId);
</script>

<section class="flex h-full flex-col gap-y-4 overflow-hidden">
	<div class="flex items-center gap-x-2">
		<button on:click={() => goto(`/educations/class/${$page.params.class_id}`)}>
			<Icon icon="mdi:chevron-left" width={32} />
		</button>

		<h1 class="text-xl font-bold">
			{subjectName}
		</h1>
	</div>

	<div class="flex h-1 flex-1 flex-col gap-3 overflow-y-auto">
		<button
			class="variant-filled-primary btn"
			on:click={() => invalidate('educations:subject:details')}
		>
			Tải lại
		</button>

		{#if contests.length === 0}
			<p class="text-center text-xl font-semibold">Hiện tại chưa có lịch thi</p>
		{/if}

		{#each contests as { id, start_date, end_date, status } (id)}
			{@const joinable =
				id === todayContestId && !status && contests.every((c) => c.status !== 'PASSED')}

			{@const disabled = !joinable}

			<button
				class={cn('app-card flex-col items-start text-left !opacity-100', {
					'border-4 border-sky-600 dark:border-sky-700': joinable,
					'border-4 border-rose-600 dark:border-rose-700':
						status === StudentExamStatus.FAILED || status === StudentExamStatus.INTERUPPTED,
					'border-4 border-emerald-600 dark:border-emerald-700': status === StudentExamStatus.PASSED
				})}
				on:click={() =>
					goto(`/contest-rules?contest_id=${id}&student_id=${student?.id}&exam_id=${examId}`)}
				{disabled}
			>
				<p>ID: {id}</p>
				<p>
					Từ ngày {chrono(start_date).toDate().toLocaleDateString()}
				</p>
				<p>
					Đến ngày {chrono(end_date).toDate().toLocaleDateString()}
				</p>

				{#if joinable}
					<p class="w-full rounded-md bg-sky-600 px-4 py-2 text-center font-semibold text-white">
						Có thể tham gia
					</p>
				{/if}

				{#if status !== null}
					<Chip
						class={cn('bg-gray-600', {
							'bg-emerald-600': status === StudentExamStatus.PASSED,
							'bg-rose-600':
								status === StudentExamStatus.FAILED || status === StudentExamStatus.INTERUPPTED,
							'bg-amber-600': status === StudentExamStatus.IN_PROGRESS
						})}
					>
						{contestStatusToVietnamese(status)}
					</Chip>
				{/if}
			</button>
		{/each}
	</div>
</section>
