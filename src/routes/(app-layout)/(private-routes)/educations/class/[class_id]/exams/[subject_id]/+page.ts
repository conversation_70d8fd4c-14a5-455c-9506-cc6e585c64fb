import type { StudentExamStatus } from '$lib/schemas/contest.schema';
import { chrono } from '$lib/utils/chrono';
import { error } from '@sveltejs/kit';

type StudentDetails = {
	id: string;
	paid_tuition_subjects: (string | undefined)[];
};

export const load = async ({ depends, parent, params, setHeaders }) => {
	depends('educations:subject:details');

	setHeaders({
		'cache-control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0'
	});

	const { supabase, session } = await parent();

	if (!session) error(401, 'Unauthorized');

	const classId = params.class_id;
	const subjectId = params.subject_id;
	const failResponse = { subject_name: 'N/A', contests: [], todayContestId: 'N/A' };

	const [studentResponse, classesResponse, classTrainingProgramResponse, contestsResponse] =
		await Promise.all([
			supabase
				.from('students')
				.select('id, paid_tuition_subjects')
				.eq('profile_id', session.user.id)
				.eq('class_id', classId)
				.single<StudentDetails>(),

			supabase.from('classes').select('name, tags').eq('id', classId).single(),

			supabase
				.from('traning_programs_to_classes')
				.select('training_programs_id')
				.eq('class_id', classId)
				.single(),

			supabase
				.from('contest')
				.select('id, start_date, end_date')
				.eq('class_id', classId)
				.order('start_date', { ascending: true })
		]);

	const student = studentResponse.data;
	const classDetail = classesResponse.data;
	const trainingProgramByClassId = classTrainingProgramResponse.data;
	let classContests = (contestsResponse.data || []).sort(
		(a, b) => new Date(a!.start_date).getTime() - new Date(b!.start_date).getTime()
	);

	if (!student || !classDetail || !trainingProgramByClassId) return failResponse;

	const { data: subject } = await supabase
		.from('subjects')
		.select('id, name, exam_id')
		.eq('id', subjectId)
		.single();

	if (!subject || !subject.exam_id) return failResponse;

	const { data: contestStatuses } = await supabase
		.from('student_to_exams')
		.select('contest_id, status')
		.eq('student_id', student.id)
		.eq('exam_id', subject.exam_id!)
		.in(
			'contest_id',
			classContests.map((item) => item.id)
		);

	const { data: contestsOnExam } = await supabase
		.from('contest_on_exam')
		.select('*')
		.eq('exam_id', subject.exam_id!)
		.in(
			'contest_id',
			classContests.map((item) => item.id)
		);

	if (!contestsOnExam) return failResponse;

	classContests = classContests.filter((contest) => {
		const contestOnExam = contestsOnExam.find((item) => item.contest_id === contest.id);
		if (!contestOnExam) return false;
		return true;
	});

	return {
		student,
		subjectName: subject.name,
		examId: subject.exam_id,
		todayContestId: classContests?.find((contest) => {
			const offsetStartDate = chrono(contest.start_date);
			const offsetEndDate = chrono(contest.end_date);

			return (
				offsetStartDate.isSameOrBefore(
					chrono().utc(false).subtract(chrono().utcOffset(), 'minutes'),
					'date'
				) &&
				offsetEndDate.isSameOrAfter(
					chrono().utc(false).subtract(chrono().utcOffset(), 'minutes'),
					'date'
				)
			);
		})?.id,
		contests: contestsOnExam.map((contestOnExam) => {
			const studentContest = contestStatuses?.find(
				(item) => item.contest_id === contestOnExam.contest_id
			);
			return {
				...classContests.find((item) => item.id === contestOnExam.contest_id)!,
				status: (studentContest?.status || null) as StudentExamStatus | null
			};
		})
	};
};
