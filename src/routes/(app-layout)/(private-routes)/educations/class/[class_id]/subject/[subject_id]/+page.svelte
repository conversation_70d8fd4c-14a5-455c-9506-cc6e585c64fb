<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { cn, getMobileOperatingSystem } from '$lib';
	import Chip from '$lib/components/Chip.svelte';
	import Icon from '@iconify/svelte';
	import Hls from 'hls.js';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import { chrono } from '$lib/utils/chrono';

	export let data: PageData;
	let { student, subjectName, lessons, supabase } = data;
	$: ({ student, subjectName, lessons, supabase } = data);

	let os: string = '';
	let hls: Hls | undefined;
	let videoRef: HTMLVideoElement;

	let isSubmitting = false;
	let playlistsElement: HTMLDivElement;
	let currentLessonIndex = Math.max(
		lessons.findIndex((lesson) => !lesson.progress?.completed_at),
		0
	);

	function handleLessonClick(index: number) {
		if (index !== currentLessonIndex && lessons[index].video_url) {
			currentLessonIndex = index;
			loadHlsVideo(lessons[index].video_url);
		}
	}

	async function handleVideoPlay(lesson: (typeof lessons)[number]) {
		if (student && lesson.progress && !lesson.progress.started_at && !isSubmitting) {
			isSubmitting = true;

			const { data: updatedProgress } = await supabase
				.from('student_to_lessons')
				.update({ started_at: chrono().toISOString() })
				.eq('student_id', student.id)
				.eq('lesson_id', lesson.lesson_id)
				.select('started_at, completed_at')
				.single();

			isSubmitting = false;

			if (updatedProgress) {
				lessons[currentLessonIndex].progress = updatedProgress;
			} else {
				if (confirm('Đã có lỗi xảy ra, bạn có muốn tải lại trang không?')) {
					location.reload();
				}
			}
		}
	}

	async function handleVideoEnd(lesson: (typeof lessons)[number]) {
		if (student && lesson.progress && !lesson.progress.completed_at && !isSubmitting) {
			isSubmitting = true;

			const { data: updatedProgress } = await supabase
				.from('student_to_lessons')
				.update({ completed_at: chrono().toISOString() })
				.eq('student_id', student.id)
				.eq('lesson_id', lesson.lesson_id)
				.select('started_at, completed_at')
				.single();

			await supabase
				.from('students')
				.update({ last_lesson_id: lesson.lesson_id })
				.eq('id', student.id);

			isSubmitting = false;

			if (updatedProgress) {
				lessons[currentLessonIndex].progress = updatedProgress;

				if (currentLessonIndex < lessons.length - 1) {
					handleLessonClick(currentLessonIndex + 1);
				}
			} else {
				if (confirm('Đã có lỗi xảy ra, bạn có muốn tải lại trang không?')) {
					location.reload();
				}
			}
		} else {
			if (currentLessonIndex < lessons.length - 1) {
				handleLessonClick(currentLessonIndex + 1);
			}
		}
	}

	function scrollToCurrentLesson() {
		if (playlistsElement) {
			const lessonElement = playlistsElement.children[currentLessonIndex] as HTMLButtonElement;

			playlistsElement.scrollTo({
				top: lessonElement.offsetTop - playlistsElement.offsetTop,
				behavior: 'smooth'
			});
		}
	}

	function loadHlsVideo(videoUrl: string) {
		if (Hls.isSupported()) {
			hls ??= new Hls();
			hls.loadSource(videoUrl);
			hls.attachMedia(videoRef);
		} else if (videoRef.canPlayType('application/vnd.apple.mpegurl')) {
			videoRef.src = videoUrl;
		}
	}

	function handleVisibilityChange() {
		if (videoRef.paused) {
			videoRef.play();
		}
	}

	onMount(() => {
		const videoUrl = lessons[currentLessonIndex].video_url;
		if (videoUrl) loadHlsVideo(videoUrl);

		os = getMobileOperatingSystem();
		if (os) {
			document.addEventListener('visibilitychange', handleVisibilityChange);
		}

		return () => {
			hls?.destroy();
			document.removeEventListener('visibilitychange', handleVisibilityChange);
		};
	});
</script>

<section class="flex h-full flex-col gap-y-4 overflow-hidden">
	<div class="flex items-center gap-x-2">
		<button on:click={() => goto(`/educations/class/${$page.params.class_id}`)}>
			<Icon icon="mdi:chevron-left" width={32} />
		</button>

		<h1 class="text-xl font-bold">
			{subjectName}
		</h1>
	</div>

	<div class="mx-auto flex h-1 max-w-2xl flex-1 flex-col gap-4 overflow-y-auto pr-2">
		<div class="aspect-video w-full bg-black">
			<!-- svelte-ignore a11y-media-has-caption -->
			<!-- svelte-ignore element_invalid_self_closing_tag -->
			<video
				class="mx-auto aspect-video size-full"
				bind:this={videoRef}
				controls
				autoplay
				playsinline
				controlsList="nodownload"
				crossorigin="anonymous"
				preload="metadata"
				on:loadeddata={scrollToCurrentLesson}
				on:play={() => handleVideoPlay(lessons[currentLessonIndex])}
				on:ended={() => handleVideoEnd(lessons[currentLessonIndex])}
			/>
		</div>

		<div bind:this={playlistsElement} class="flex h-1 flex-1 flex-col gap-3 lg:h-auto">
			{#each lessons as lesson, index}
				{@const active = currentLessonIndex === index}
				{@const disabled = index > 0 && !lessons[index - 1].progress?.completed_at}

				<button
					class={cn('app-card flex-col items-start text-left opacity-90', {
						'border-4 !border-sky-600 opacity-100': active,
						'!border-emerald-600': lesson.progress?.completed_at
					})}
					on:click={() => handleLessonClick(index)}
					{disabled}
				>
					<p class={cn({ 'font-semibold': active })}>{lesson.name}</p>
					<Chip
						class={cn('bg-emerald-600', {
							'bg-sky-600': !lesson.progress?.completed_at,
							'bg-slate-600': disabled
						})}
					>
						{#if disabled}
							Hoàn thành bài học trước để mở khóa
						{:else if lesson.progress}
							{#if lesson.progress.completed_at}
								Đã hoàn thành
								{new Date(lesson.progress.completed_at).toLocaleString()}
							{:else}
								Đang học
							{/if}
						{/if}
					</Chip>
				</button>
			{/each}
		</div>
	</div>
</section>
