import { error } from '@sveltejs/kit';

type StudentDetails = {
	id: string;
};

export const load = async ({ depends, parent, params }) => {
	depends('educations:subject:details');

	const { session, supabase } = await parent();

	if (!session) error(401, 'Unauthorized');

	const classId = params.class_id;
	const subjectId = params.subject_id;
	const failResponse = { subject_name: 'N/A', lessons: [] };

	const { data: student } = await supabase
		.from('students')
		.select('id')
		.eq('profile_id', session.user.id)
		.eq('class_id', classId)
		.single<StudentDetails>();

	const { data: subjectLessons } = await supabase
		.from('subject_to_lessons')
		.select('order, subject_id, lesson_id')
		.eq('subject_id', subjectId)
		.order('order', { ascending: true });

	const { data: subject } = await supabase
		.from('subjects')
		.select('id, name, exam_id')
		.eq('id', subjectId)
		.single();

	if (!subject || !subjectLessons || !student) return failResponse;

	const { data: studentLessons } = await supabase
		.from('student_to_lessons')
		.select('completed_at, lesson_id')
		.eq('student_id', student.id);

	if (!studentLessons) return failResponse;

	const { data: lessonsDetails } = await supabase
		.from('lessons')
		.select('id, name, video_id')
		.in(
			'id',
			subjectLessons.map((item) => item.lesson_id)
		);

	if (!lessonsDetails) return failResponse;

	return {
		student,
		subjectName: subject.name,
		lessons: await Promise.all(
			subjectLessons.map(async (lesson) => {
				const lessonDetail = lessonsDetails.find((item) => item.id === lesson.lesson_id);

				if (!lessonDetail) return { ...lesson, name: 'N/A', video_url: null, progress: null };

				const { data: resource } = await supabase
					.from('resources')
					.select('path')
					.eq('id', lessonDetail.video_id)
					.single();

				if (!resource) return { ...lesson, name: 'N/A', video_url: null, progress: null };

				const { data: progress } = await supabase
					.from('student_to_lessons')
					.upsert({ student_id: student.id, lesson_id: lessonDetail.id })
					.select('started_at, completed_at')
					.single();

				if (!progress) return { ...lesson, name: 'N/A', video_url: null, progress: null };

				const {
					data: { publicUrl }
				} = supabase.storage.from('videos').getPublicUrl(resource.path);

				return {
					...lesson,
					name: lessonDetail.name,
					video_url: publicUrl,
					progress
				};
			})
		)
	};
};
