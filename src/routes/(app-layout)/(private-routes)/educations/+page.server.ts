import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { StudentStatus } from '$lib/supabase/types-utils';

const STATUS_ORDER: Record<string, number> = {
	[StudentStatus.ENROLLED]: 1,
	[StudentStatus.ACTIVE]: 2,
	[StudentStatus.GRADUATED]: 3,
	[StudentStatus.SUSPENDED]: 4
};

export const load = (async ({ depends, locals: { supabase, session } }) => {
	depends('educations:classes:list');

	if (!session) error(401, 'Unauthorized');

	const { data: students } = await supabase
		.from('students')
		.select('class_id, status')
		.eq('profile_id', session.user.id);

	if (!students) return { classList: [] };

	const studentsWithClass = await Promise.all(
		students?.map(async (student) => {
			const { data: classData } = await supabase
				.from('classes')
				.select('name')
				.eq('id', student.class_id)
				.single();

			return {
				...student,
				class_name: classData ? classData.name : 'N/A'
			};
		})
	);

	return {
		classList: studentsWithClass.sort((a, b) => STATUS_ORDER[a.status] - STATUS_ORDER[b.status])
	};
}) satisfies PageServerLoad;
