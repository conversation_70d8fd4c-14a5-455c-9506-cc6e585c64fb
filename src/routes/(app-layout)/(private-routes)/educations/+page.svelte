<script lang="ts">
	import { goto } from '$app/navigation';
	import { cn } from '$lib';
	import { StudentStatus } from '$lib/supabase/types-utils';
	import { statusToVietnamese } from '$lib/translate';
	import type { PageData } from './$types';

	export let data: PageData;
	let { classList } = data;
</script>

{#if classList.length}
	<h1 class="mb-4 text-2xl font-bold">Danh sách lớp học</h1>

	<section class="flex flex-col gap-y-4">
		{#each classList as data}
			<button class="app-card" on:click={() => goto(`/educations/class/${data.class_id}`)}>
				<div class="flex flex-col items-start gap-y-3 font-semibold">
					<h2 class="text-xl">{data.class_name}</h2>
					<p
						class={cn('rounded-full bg-emerald-600 px-3 py-0.5 text-sm text-white', {
							'bg-sky-600': data.status === StudentStatus.GRADUATED,
							'bg-rose-600': data.status === StudentStatus.SUSPENDED
						})}
					>
						{statusToVietnamese(data.status)}
					</p>
				</div>
			</button>
		{/each}
	</section>
{/if}
