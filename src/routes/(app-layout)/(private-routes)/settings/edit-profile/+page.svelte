<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import { cn } from '$lib';
	import { Gender } from '$lib/schemas/student.schema.js';
	import Icon from '@iconify/svelte';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import { Control, Field, FieldErrors, Label } from 'formsnap';
	import { superForm } from 'sveltekit-superforms';

	export let data;
	let { form } = data;
	$: ({ form } = data);

	const toastStore = getToastStore();

	const profileForm = superForm(form, {
		taintedMessage: 'Bạn có muốn rời khỏi trang này? Các thay đổi sẽ không được lưu lại.',
		onResult({ result }) {
			if (result.type === 'success') {
				toastStore.trigger({
					background: 'variant-filled-success',
					message: result.data?.form.message,
					timeout: 1000,
					hideDismiss: true
				});
			} else if (result.type === 'failure') {
				toastStore.trigger({
					background: 'variant-filled-error',
					message: result.data?.form.message,
					timeout: 1000,
					hideDismiss: true
				});
			} else {
				toastStore.trigger({
					background: 'variant-filled-error',
					message: '<PERSON>ã có lỗi xảy ra. Vui lòng thử lại sau.',
					timeout: 1000,
					hideDismiss: true
				});
			}
		},
		onUpdated({ form }) {
			if (form.valid) invalidate('profile:edit');
		}
	});
	const { form: formData, enhance, tainted } = profileForm;
</script>

<form
	class="flex w-full flex-1 flex-col gap-y-4 overflow-auto p-0.5 md:w-2/3"
	method="POST"
	use:enhance
>
	<div class="relative flex h-8 w-full items-center justify-center">
		<button
			type="button"
			class="absolute left-0 top-1/2 -translate-y-1/2"
			on:click={() => goto('/settings')}
		>
			<Icon icon="mdi:chevron-left" width={28} />
		</button>

		<h1 class="text-xl font-semibold">Cập nhật hồ sơ</h1>

		<button
			class={cn('btn-sm absolute right-0 top-1/2 -translate-y-1/2 rounded-full', {
				'variant-filled-success': $tainted,
				'variant-outline-success opacity-75': !$tainted
			})}
			disabled={!$tainted}
		>
			Lưu
		</button>
	</div>

	<hr />

	<Field form={profileForm} name="user_id">
		<Control let:attrs>
			<input {...attrs} bind:value={$formData.user_id} hidden />
		</Control>
	</Field>

	<Field form={profileForm} name="email">
		<Control let:attrs>
			<input {...attrs} type="email" bind:value={$formData.email} hidden />
		</Control>
	</Field>

	<section class="flex-1 space-y-3">
		<Field form={profileForm} name="app_full_name">
			<Control let:attrs>
				<Label>Họ và tên</Label>
				<input {...attrs} bind:value={$formData.app_full_name} />
			</Control>
			<FieldErrors />
		</Field>

		<Field form={profileForm} name="gender">
			<Control let:attrs>
				<div class="flex flex-col items-start gap-1">
					<Label>Giới tính</Label>
					<select {...attrs} bind:value={$formData.gender}>
						{#each [{ value: Gender.MALE, label: 'Nam' }, { value: Gender.FEMALE, label: 'Nữ' }] as { label, value }}
							<option {value}>
								{label}
							</option>
						{/each}
					</select>
				</div>
			</Control>
			<FieldErrors />
		</Field>

		<Field form={profileForm} name="phone">
			<Control let:attrs>
				<Label>Số điện thoại</Label>
				<input {...attrs} bind:value={$formData.phone} />
			</Control>
			<FieldErrors />
		</Field>

		<Field form={profileForm} name="address">
			<Control let:attrs>
				<Label>Địa chỉ</Label>
				<input {...attrs} bind:value={$formData.address} />
			</Control>
			<FieldErrors />
		</Field>

		<Field form={profileForm} name="previous_theology_education">
			<Control let:attrs>
				<div class="flex flex-col items-start gap-1">
					<Label>Học vấn thần học:</Label>
					<select {...attrs} bind:value={$formData.previous_theology_education}>
						{#each [{ value: true, label: 'Đã học qua lớp thần học' }, { value: false, label: 'Chưa từng học qua lớp thần học' }] as { label, value }}
							<option {value}>
								{label}
							</option>
						{/each}
					</select>
				</div>
			</Control>
			<FieldErrors />
		</Field>
	</section>
</form>
