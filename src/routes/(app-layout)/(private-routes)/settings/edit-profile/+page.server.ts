import { studentMetadataSchema, type StudentMetadataSchema } from '$lib/schemas/student.schema.js';
import { error, fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';

export const load = async ({ locals: { supabase, session }, depends }) => {
	depends('profile:edit');

	if (!session) error(401, 'Unauthorized');

	const { data: profile } = await supabase
		.from('profiles')
		.select('*')
		.eq('id', session.user.id)
		.maybeSingle();

	const studentMetadata: Partial<StudentMetadataSchema> = {
		user_id: session.user.id,
		email: session!.user.email
	};

	if (profile) {
		Object.assign(studentMetadata, profile.metadata);
	}

	const form = await superValidate(studentMetadata, zod(studentMetadataSchema), { errors: false });

	return { form };
};

export const actions = {
	default: async ({ request, locals: { supabase } }) => {
		const form = await superValidate(request, zod(studentMetadataSchema));

		if (!form.valid) return fail(400, { form });

		const { user_id, email, ...rest } = form.data;
		const upsertProfileResponse = await supabase.from('profiles').upsert({
			id: user_id,
			email,
			metadata: rest
		});

		if (upsertProfileResponse.error) {
			return message(form, 'Có lỗi xảy ra khi cập nhật thông tin cá nhân!', {
				status: 400
			});
		}

		return message(form, 'Thông tin cá nhân đã được cập nhật thành công!');
	}
};
