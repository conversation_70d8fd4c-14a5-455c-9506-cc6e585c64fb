<script lang="ts" context="module">
	type MenuItem = {
		customClass?: string;
		href?: string;
		label: string;
	};
</script>

<script lang="ts">
	import { goto } from '$app/navigation';

	import { cn } from '$lib';

	let menu: MenuItem[] = [
		{
			label: 'Build: ' + new Date(__DATE__).toLocaleString().replace(/[^\w]/g, '')
		},
		{
			href: '/settings/edit-profile',
			label: 'Chỉnh sửa hồ sơ'
		},
		{
			customClass: 'text-red-500 font-semibold',
			href: '/auth/logout',
			label: 'Đăng xuất'
		}
	];
</script>

<ul class="">
	{#each menu as { customClass, href, label }, i}
		<li class="w-full border-b border-neutral-600 p-4 dark:border-neutral-200">
			{#if href}
				<button class={cn('block w-full text-left', customClass)} on:click={() => goto(href)}
					>{label}</button
				>
			{:else}
				<span class={cn('block text-neutral-600 dark:text-gray-400', customClass)}>{label}</span>
			{/if}
		</li>
	{/each}
</ul>
