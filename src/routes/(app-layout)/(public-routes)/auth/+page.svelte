<script>
	import { page } from '$app/stores';
	import { onMount } from 'svelte';

	export let data;
	let { supabase, session } = data;

	async function signInWithGoogle() {
		await supabase.auth.signInWithOAuth({
			provider: 'google',
			options: {
				queryParams: { prompt: 'select_account' },
				redirectTo: `${$page.url.origin}/auth/callback`
			}
		});
	}
</script>

<div class="flex size-full flex-col items-center justify-center gap-y-4 px-4">
	<h2 class="h2"><PERSON>ui lòng đăng nhập để tiếp tục</h2>

	<div class="w-full space-y-2 text-center">
		<button
			type="button"
			class="variant-filled-success btn w-full max-w-96"
			on:click={signInWithGoogle}
		>
			<PERSON><PERSON><PERSON> nhập bằng Google
		</button>
	</div>
</div>
