<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';

	export let data: PageData;
	$: ({ session, supabase } = data);

	onMount(async () => {
		await supabase.auth.signOut({ scope: 'global' });
		await invalidate('supabase:auth');
		await goto('/');
	});
</script>

<section class="flex size-full items-center justify-center">
	<p>Logging out...</p>
</section>
