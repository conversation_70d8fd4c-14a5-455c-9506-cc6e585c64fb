import { redirect } from '@sveltejs/kit';

export const GET = async (event) => {
	const {
		url,
		locals: { supabase }
	} = event;
	const code = url.searchParams.get('code') as string;
	const next = url.searchParams.get('next') ?? '/audio-books';

	if (code) {
		const { data, error } = await supabase.auth.exchangeCodeForSession(code);
		if (!error) {
			await supabase.from('user_roles').upsert({
				user_id: data.user.id,
				role: 'user'
			});
			await supabase.from('user_roles').upsert({
				user_id: data.user.id,
				role: 'student'
			});
			throw redirect(303, `/${next.slice(1)}`);
		}
	}

	// return the user to an error page with instructions
	throw redirect(303, '/auth/auth-code-error');
};
