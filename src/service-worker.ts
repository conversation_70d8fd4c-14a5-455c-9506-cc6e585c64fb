/// <reference lib="WebWorker" />
/// <reference types="vite/client" />
/// <reference no-default-lib="true"/>
/// <reference lib="esnext" />
import { cleanupOutdatedCaches, precacheAndRoute } from 'workbox-precaching';

declare let self: ServiceWorkerGlobalScope;

self.addEventListener('message', (event) => {
	if (event.data && event.data.type === 'SKIP_WAITING') self.skipWaiting();
});

// registerRoute(
// 	({ request }) => {
// 		const { destination } = request;
// 		return destination === 'video' || destination === 'audio';
// 	},

// 	new CacheFirst({
// 		cacheName: cacheNames.precache,
// 		plugins: [
// 			new CacheableResponsePlugin({
// 				statuses: [0, 200]
// 			}),
// 			new RangeRequestsPlugin(),
// 			new ExpirationPlugin({
// 				maxEntries: 10, // Limit the number of cached items
// 				maxAgeSeconds: 15 * 24 * 60 * 60 // Expire items after 15 days
// 			})
// 		]
// 	})
// );

// self.__WB_MANIFEST is default injection point
precacheAndRoute(self.__WB_MANIFEST);

// clean old assets
cleanupOutdatedCaches();
