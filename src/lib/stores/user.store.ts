import type { Database } from '$lib/supabase/database.types';
import { readonly, writable } from 'svelte/store';

export type UserStore = {
	roles: Database['public']['Enums']['app_role'][] | null;
};

const _userStore = writable<UserStore>({
	roles: null
});

const userStore = readonly<UserStore>(_userStore);

const setRole = (role: UserStore['roles']) => {
	_userStore.update((user) => {
		user.roles = role;
		return user;
	});
};

export { userStore, setRole };
