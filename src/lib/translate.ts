import { StudentExamStatus } from '$lib/schemas/contest.schema';
import { StudentStatus } from '$lib/supabase/types-utils';

export const statusToVietnamese = (status: string) => {
	switch (status) {
		case StudentStatus.ENROLLED:
		case StudentStatus.ACTIVE:
			return '<PERSON>ang học';
		case StudentStatus.GRADUATED:
			return 'Đã tốt nghiệp';
		case StudentStatus.SUSPENDED:
			return 'Đã bị đình chỉ';
		default:
			return 'Không xác định';
	}
};

export const contestStatusToVietnamese = (status: StudentExamStatus) => {
	switch (status) {
		case StudentExamStatus.ENTRY:
			return 'Chưa làm';

		case StudentExamStatus.IN_PROGRESS:
			return 'Đang làm';

		case StudentExamStatus.PASSED:
			return 'Đạt';

		case StudentExamStatus.FAILED:
			return 'Không đạt';

		case StudentExamStatus.INTERUPPTED:
			return 'Bị gián đo<PERSON>n';

		default:
			return 'Không xác định';
	}
};
