export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      answers: {
        Row: {
          content: string
          created_at: string | null
          id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
        }
        Relationships: []
      }
      audiobook_type_mapping: {
        Row: {
          audiobook_id: string
          created_at: string
          id: string
          type_id: string
        }
        Insert: {
          audiobook_id: string
          created_at?: string
          id?: string
          type_id: string
        }
        Update: {
          audiobook_id?: string
          created_at?: string
          id?: string
          type_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "audiobook_type_mapping_audiobook_id_fkey"
            columns: ["audiobook_id"]
            isOneToOne: false
            referencedRelation: "audiobooks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audiobook_type_mapping_type_id_fkey"
            columns: ["type_id"]
            isOneToOne: false
            referencedRelation: "audiobook_types"
            referencedColumns: ["id"]
          },
        ]
      }
      audiobook_types: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      audiobooks: {
        Row: {
          author: string
          cover_image: string
          created_at: string
          id: string
          slug: string
          title: string
        }
        Insert: {
          author: string
          cover_image: string
          created_at: string
          id?: string
          slug: string
          title: string
        }
        Update: {
          author?: string
          cover_image?: string
          created_at?: string
          id?: string
          slug?: string
          title?: string
        }
        Relationships: []
      }
      chapters: {
        Row: {
          audiobook_id: string
          created_at: string
          id: string
          number: number
          title: string
          updated_at: string
        }
        Insert: {
          audiobook_id: string
          created_at?: string
          id?: string
          number: number
          title: string
          updated_at?: string
        }
        Update: {
          audiobook_id?: string
          created_at?: string
          id?: string
          number?: number
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "chapters_audiobook_id_fkey"
            columns: ["audiobook_id"]
            isOneToOne: false
            referencedRelation: "audiobooks"
            referencedColumns: ["id"]
          },
        ]
      }
      classes: {
        Row: {
          created_at: string
          end_at: string | null
          id: string
          name: string
          start_at: string | null
          tags: string | null
        }
        Insert: {
          created_at?: string
          end_at?: string | null
          id?: string
          name: string
          start_at?: string | null
          tags?: string | null
        }
        Update: {
          created_at?: string
          end_at?: string | null
          id?: string
          name?: string
          start_at?: string | null
          tags?: string | null
        }
        Relationships: []
      }
      contest: {
        Row: {
          class_id: string | null
          created_at: string
          end_date: string
          id: string
          start_date: string
          type: string
        }
        Insert: {
          class_id?: string | null
          created_at?: string
          end_date: string
          id?: string
          start_date: string
          type?: string
        }
        Update: {
          class_id?: string | null
          created_at?: string
          end_date?: string
          id?: string
          start_date?: string
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "contest_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
        ]
      }
      contest_on_exam: {
        Row: {
          contest_id: string
          created_at: string
          exam_id: string
        }
        Insert: {
          contest_id: string
          created_at?: string
          exam_id: string
        }
        Update: {
          contest_id?: string
          created_at?: string
          exam_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "public_contest_on_exam_contest_id_fkey"
            columns: ["contest_id"]
            isOneToOne: false
            referencedRelation: "contest"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "public_contest_on_exam_exam_id_fkey"
            columns: ["exam_id"]
            isOneToOne: false
            referencedRelation: "exams"
            referencedColumns: ["id"]
          },
        ]
      }
      exams: {
        Row: {
          created_at: string | null
          id: string
          max_score: number
          min_score_to_pass: number
          name: string | null
          questions: Json | null
          time_limit: number
        }
        Insert: {
          created_at?: string | null
          id?: string
          max_score?: number
          min_score_to_pass?: number
          name?: string | null
          questions?: Json | null
          time_limit: number
        }
        Update: {
          created_at?: string | null
          id?: string
          max_score?: number
          min_score_to_pass?: number
          name?: string | null
          questions?: Json | null
          time_limit?: number
        }
        Relationships: []
      }
      lessons: {
        Row: {
          created_at: string | null
          id: string
          name: string
          outline: string | null
          video_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          outline?: string | null
          video_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          outline?: string | null
          video_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "lessons_video_id_fkey"
            columns: ["video_id"]
            isOneToOne: false
            referencedRelation: "resources"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          created_at: string
          email: string
          id: string
          metadata: Json | null
        }
        Insert: {
          created_at?: string
          email: string
          id: string
          metadata?: Json | null
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          metadata?: Json | null
        }
        Relationships: []
      }
      questions: {
        Row: {
          answers: Json
          created_at: string | null
          id: string
          question: string
        }
        Insert: {
          answers?: Json
          created_at?: string | null
          id?: string
          question: string
        }
        Update: {
          answers?: Json
          created_at?: string | null
          id?: string
          question?: string
        }
        Relationships: []
      }
      questions_on_exams: {
        Row: {
          created_at: string
          exam_id: string
          question_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          exam_id: string
          question_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          exam_id?: string
          question_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "questions_on_exams_exam_id_fkey"
            columns: ["exam_id"]
            isOneToOne: false
            referencedRelation: "exams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "questions_on_exams_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: false
            referencedRelation: "questions"
            referencedColumns: ["id"]
          },
        ]
      }
      resources: {
        Row: {
          audio_path: string | null
          created_at: string
          id: string
          name: string
          path: string
        }
        Insert: {
          audio_path?: string | null
          created_at?: string
          id?: string
          name: string
          path: string
        }
        Update: {
          audio_path?: string | null
          created_at?: string
          id?: string
          name?: string
          path?: string
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          id: string
          permission: Database["public"]["Enums"]["app_permission"]
          role: Database["public"]["Enums"]["app_role"]
        }
        Insert: {
          id?: string
          permission: Database["public"]["Enums"]["app_permission"]
          role: Database["public"]["Enums"]["app_role"]
        }
        Update: {
          id?: string
          permission?: Database["public"]["Enums"]["app_permission"]
          role?: Database["public"]["Enums"]["app_role"]
        }
        Relationships: []
      }
      roles: {
        Row: {
          roles: Database["public"]["Enums"]["role"][]
          user_id: string
        }
        Insert: {
          roles: Database["public"]["Enums"]["role"][]
          user_id: string
        }
        Update: {
          roles?: Database["public"]["Enums"]["role"][]
          user_id?: string
        }
        Relationships: []
      }
      student_to_exams: {
        Row: {
          contest_id: string
          created_at: string | null
          ended_at: string | null
          exam_id: string
          started_at: string | null
          status: string | null
          student_id: string
          submission: Json | null
        }
        Insert: {
          contest_id: string
          created_at?: string | null
          ended_at?: string | null
          exam_id: string
          started_at?: string | null
          status?: string | null
          student_id: string
          submission?: Json | null
        }
        Update: {
          contest_id?: string
          created_at?: string | null
          ended_at?: string | null
          exam_id?: string
          started_at?: string | null
          status?: string | null
          student_id?: string
          submission?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "public_student_to_exams_contest_id_fkey"
            columns: ["contest_id"]
            isOneToOne: false
            referencedRelation: "contest"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_to_exams_exam_id_fkey"
            columns: ["exam_id"]
            isOneToOne: false
            referencedRelation: "exams"
            referencedColumns: ["id"]
          },
        ]
      }
      student_to_lessons: {
        Row: {
          completed_at: string | null
          created_at: string | null
          lesson_id: string
          started_at: string | null
          student_id: string
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          lesson_id: string
          started_at?: string | null
          student_id: string
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          lesson_id?: string
          started_at?: string | null
          student_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_to_lessons_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "lessons"
            referencedColumns: ["id"]
          },
        ]
      }
      students: {
        Row: {
          class_id: string
          created_at: string
          graduated_at: string | null
          id: string
          last_lesson_id: string | null
          paid_tuition_subjects: Json[]
          profile_id: string
          skipped_subjects: Json[]
          status: string
        }
        Insert: {
          class_id: string
          created_at?: string
          graduated_at?: string | null
          id?: string
          last_lesson_id?: string | null
          paid_tuition_subjects?: Json[]
          profile_id: string
          skipped_subjects?: Json[]
          status?: string
        }
        Update: {
          class_id?: string
          created_at?: string
          graduated_at?: string | null
          id?: string
          last_lesson_id?: string | null
          paid_tuition_subjects?: Json[]
          profile_id?: string
          skipped_subjects?: Json[]
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "students_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      subject_to_lessons: {
        Row: {
          created_at: string
          lesson_id: string
          order: number
          subject_id: string
        }
        Insert: {
          created_at?: string
          lesson_id: string
          order: number
          subject_id: string
        }
        Update: {
          created_at?: string
          lesson_id?: string
          order?: number
          subject_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subject_to_lessons_lesson_id_fkey"
            columns: ["lesson_id"]
            isOneToOne: false
            referencedRelation: "lessons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subject_to_lessons_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      subjects: {
        Row: {
          created_at: string
          exam_id: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          exam_id?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          exam_id?: string | null
          id?: string
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "subjects_exam_id_fkey"
            columns: ["exam_id"]
            isOneToOne: false
            referencedRelation: "exams"
            referencedColumns: ["id"]
          },
        ]
      }
      training_program_to_subjects: {
        Row: {
          created_at: string | null
          order: number
          subject_id: string
          training_programs_id: string
        }
        Insert: {
          created_at?: string | null
          order: number
          subject_id: string
          training_programs_id: string
        }
        Update: {
          created_at?: string | null
          order?: number
          subject_id?: string
          training_programs_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "training_program_to_subjects_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "training_program_to_subjects_training_programs_id_fkey"
            columns: ["training_programs_id"]
            isOneToOne: false
            referencedRelation: "training_programs"
            referencedColumns: ["id"]
          },
        ]
      }
      training_programs: {
        Row: {
          created_at: string
          description: string
          id: string
          name: string
        }
        Insert: {
          created_at?: string
          description: string
          id?: string
          name: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      traning_programs_to_classes: {
        Row: {
          class_id: string
          created_at: string
          training_programs_id: string
        }
        Insert: {
          class_id: string
          created_at?: string
          training_programs_id: string
        }
        Update: {
          class_id?: string
          created_at?: string
          training_programs_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "traning_programs_to_classes_class_id_fkey"
            columns: ["class_id"]
            isOneToOne: false
            referencedRelation: "classes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "traning_programs_to_classes_training_programs_id_fkey"
            columns: ["training_programs_id"]
            isOneToOne: false
            referencedRelation: "training_programs"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          id?: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      authorize: {
        Args: {
          requested_permission: Database["public"]["Enums"]["app_permission"]
        }
        Returns: boolean
      }
      create_lesson_with_subject: {
        Args: {
          lesson_data: Json[]
          subject_name: string
        }
        Returns: undefined
      }
      custom_access_token_hook: {
        Args: {
          event: Json
        }
        Returns: Json
      }
      delete_claim: {
        Args: {
          uid: string
          claim: string
        }
        Returns: string
      }
      get_claim: {
        Args: {
          uid: string
          claim: string
        }
        Returns: Json
      }
      get_claims: {
        Args: {
          uid: string
        }
        Returns: Json
      }
      get_my_claim: {
        Args: {
          claim: string
        }
        Returns: Json
      }
      get_my_claims: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_subject_details: {
        Args: {
          subject_id_param: string
        }
        Returns: {
          name: string
          lessons: Json
        }[]
      }
      get_user_claim: {
        Args: {
          claim: string
        }
        Returns: Json
      }
      is_claims_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      set_claim: {
        Args: {
          uid: string
          claim: string
          value: Json
        }
        Returns: string
      }
      training_program_create: {
        Args: {
          name_param: string
          description_param: string
          subjects_param: Json[]
        }
        Returns: undefined
      }
      training_program_get_details: {
        Args: {
          training_program_id_param: string
        }
        Returns: {
          id: string
          name: string
          description: string
          subjects: Json
        }[]
      }
      training_program_update_details: {
        Args: {
          id_param: string
          name_param: string
          description_param: string
          subjects_param: Json[]
        }
        Returns: undefined
      }
      update_subject_details: {
        Args: {
          subject_id_param: string
          subject_name_param: string
          lessons_param: Json[]
        }
        Returns: undefined
      }
    }
    Enums: {
      app_permission:
        | "classes.list"
        | "classes.update"
        | "classes.create"
        | "classes.delete"
        | "classes.read"
      app_role: "admin" | "student" | "user"
      role: "superadmin" | "admin" | "teacher" | "student" | "user"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
