import { z } from 'zod';

export enum Gender {
	MALE = 'MALE',
	FEMALE = 'FEMALE'
}

export const studentMetadataSchema = z.object({
	user_id: z.string().uuid(),
	email: z.string().email({ message: '<PERSON><PERSON> không hợp lệ' }),
	app_full_name: z.string().min(1, '<PERSON><PERSON> và tên không được để trống'),
	gender: z.nativeEnum(Gender, {
		required_error: '<PERSON>iớ<PERSON> tính không được để trống'
	}),
	phone: z.string(),
	address: z.string().min(1, 'Đ<PERSON>a chỉ không được để trống'),
	previous_theology_education: z.boolean()
});

export type StudentMetadataSchema = z.infer<typeof studentMetadataSchema>;
