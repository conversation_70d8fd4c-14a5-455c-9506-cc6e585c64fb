import { z } from 'zod';

export const startContestSchema = z.object({
	contestId: z.string(),
	examId: z.string(),
	studentId: z.string()
});

export type StartContestSchema = z.infer<typeof startContestSchema>;

export enum StudentExamStatus {
	ENTRY = 'ENTRY',
	IN_PROGRESS = 'IN_PROGRESS',
	PASSED = 'PASSED',
	FAILED = 'FAILED',
	INTERUPPTED = 'INTERUPPTED'
}

export type Question = {
	id: string;
	type: string;
	question: string;
	answers: {
		id: string;
		answer: string;
		selected: boolean;
		is_correct?: boolean;
	}[];
};

export const examSubmissionSchema = z.object({
	contestId: z.string(),
	examId: z.string(),
	studentId: z.string(),
	submissions: z
		.object({
			questionId: z.string(),
			answerId: z.string()
		})
		.array()
});

export type ExamSubmissionSchema = z.infer<typeof examSubmissionSchema>;

export const examInterruptionSchema = z.object({
	contestId: z.string(),
	examId: z.string(),
	studentId: z.string(),
	submissions: z
		.object({
			questionId: z.string(),
			answerId: z.string()
		})
		.array()
});
export type ExamInterruptionSchema = z.infer<typeof examInterruptionSchema>;
