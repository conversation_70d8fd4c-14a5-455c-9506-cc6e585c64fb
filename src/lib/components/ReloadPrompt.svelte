<script lang="ts">
	import { useRegisterSW } from 'virtual:pwa-register/svelte';

	const { needRefresh, updateServiceWorker } = useRegisterSW({
		onRegistered(r) {
			if (__RELOAD_SW__) {
				r &&
					setInterval(() => {
						console.log('Checking for sw update');
						r.update();
					}, 20000 /* 20s for testing purposes */);
			} else {
				console.log(`SW Registered`);
				console.log(`${JSON.stringify(r, null, 2)}`);
			}
		},
		onRegisterError(error) {
			console.log('SW registration error', error);
		}
	});

	$: if ($needRefresh) updateServiceWorker(true);
</script>
