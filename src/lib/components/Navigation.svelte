<script lang="ts">
	import { page } from '$app/stores';
	import { cn, getMobileOperatingSystem } from '$lib';
	import Icon from '@iconify/svelte';
	import { onMount } from 'svelte';

	let os: string = '';
	let menu = [
		{
			icon: 'mdi:headphones',
			label: 'Sách nói',
			link: '/audio-books'
		},
		{
			icon: 'mdi:school',
			label: '<PERSON>ọc tập',
			link: '/educations'
		},
		{
			icon: 'mdi:qrcode-scan',
			label: '',
			link: '/scan'
		},
		{
			icon: 'mdi:notifications',
			label: 'Thông báo',
			link: '/notifications'
		},
		{
			icon: 'mdi:settings',
			label: 'Cài đặt',
			link: '/settings'
		}
	];

	onMount(() => {
		os = getMobileOperatingSystem();
	});
</script>

<div
	class="relative flex w-full items-center justify-center bg-neutral-100 p-4 dark:bg-neutral-900"
>
	<ul class="flex w-full">
		{#each menu as { icon, label, link }, i}
			<li class={cn('relative z-10 w-1/5 list-none')}>
				<a
					class="flex h-full flex-col items-center justify-center text-center text-neutral-800/65 dark:text-white/50"
					href={link}
				>
					<span
						class={cn('block text-2xl transition-all duration-300', {
							'font-semibold text-teal-800 dark:text-white': $page.url.pathname.startsWith(link),
							'text-4xl': !label
						})}
					>
						<Icon class="mx-auto" {icon} />
					</span>
					{#if label}
						<span
							class={cn('text-nowrap text-xs transition-all duration-300', {
								'font-semibold text-teal-800 dark:text-white': $page.url.pathname.startsWith(link)
							})}
						>
							{label}
						</span>
					{/if}
				</a>
			</li>
		{/each}
	</ul>
</div>
