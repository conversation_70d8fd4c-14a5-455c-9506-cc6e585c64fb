import adapter from '@sveltejs/adapter-vercel';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		// adapter-auto only supports some environments, see https://kit.svelte.dev/docs/adapter-auto for a list.
		// If your environment is not supported, or you settled on a specific environment, switch out the adapter.
		// See https://kit.svelte.dev/docs/adapters for more information about adapters.
		adapter: adapter({ maxDuration: 60 }),
		serviceWorker: {
			register: false
		},
		files: {
			// you don't need to do this if you're using generateSW strategy in your app
			serviceWorker: 'src/service-worker.ts'
		},
		csrf: {
			checkOrigin: true
		},
		env: {
			publicPrefix: 'NEXT_PUBLIC'
		}
	}
};

export default config;
